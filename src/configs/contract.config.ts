import IdFactoryAbi from '../abis/idFactory.abi.json';
import TREXFactoryAbi from '../abis/TREXFactory.abi.json';
import FundFactoryAbi from '../abis/fundFactory.abi.json';
import EscrowFactoryAbi from '../abis/escrowController.abi.json';
import NFTFactoryAbi from '../abis/NFTFactory.abi.json';
import BondingFactoryAbi from '../abis/bondingFactory.abi.json';
import MarketplaceProxyAbi from '../abis/marketplaceProxy.abi.json';
import { ContractAbi } from 'web3';

export interface IContract {
  name: string;
  address: string;
  events: string[];
  startBlock: number;
  abi: ContractAbi;
}

// Base chain contracts
export const baseContractsConfig: IContract[] = [
  {
    name: 'IDFACTORY',
    address: process.env.IDFACTORY, //IdFactory
    events: ['IDCreated', 'AgentAdded'],
    startBlock: Number(process.env.START_BLOCK) || 0,
    abi: IdFactoryAbi,
  },
  {
    name: 'TREXFACTORY',
    address: process.env.TREXFACTORY, //TREX Factory
    events: ['TREXSuiteDeployed'],
    startBlock: Number(process.env.START_BLOCK) || 0,
    abi: TREXFactoryAbi,
  },
  {
    name: 'FUNDFACTORYPROXY',
    address: process.env.FUNDFACTORYPROXY, // Fund Proxy
    events: ['EquityConfigCreated', 'FundCreated', 'AdminFeeUpdated', 'AdminWalletUpdated'],
    startBlock: Number(process.env.START_BLOCK) || 0,
    abi: FundFactoryAbi,
  },
  {
    name: 'ESCROWCONTROLLERPROXY',
    address: process.env.ESCROWCONTROLLERPROXY, // EscrowControllerProxy
    events: ['OrderCreated', 'OrderSettled', 'UserTokensFrozen', 'UserTokensUnFrozen', 'ForceTransferred', 'DividendDistributed', 'RedemptionAndBurn', 'ValuationUpdated', 'NAVUpdated', 'OrderCancelled', 'UserIdentityRegistered'],
    startBlock: Number(process.env.START_BLOCK) || 0,
    abi: EscrowFactoryAbi,
  },
  {
    name: 'NFTFACTORYPROXY',
    address: process.env.NFTFACTORYPROXY, //   NFT Factory Proxy:
    events: ['CollectionCreated'],
    startBlock: Number(process.env.START_BLOCK) || 0,
    abi: NFTFactoryAbi,
  },
  {
    name: 'BONDINGFACTORYPROXY',
    address: process.env.BONDINGFACTORYPROXY, //Bonding Factory Proxy
    events: ['BondingTokenCreated'],
    startBlock: Number(process.env.START_BLOCK) || 0,
    abi: BondingFactoryAbi,
  },
  {
    name: 'NFTMARKETPLACEPROXY',
    address: process.env.NFTMARKETPLACEPROXY, //Market Place Proxy
    events: ['Purchase'],
    startBlock: Number(process.env.START_BLOCK) || 0,
    abi: MarketplaceProxyAbi,
  },
];

// BSC chain contracts
export const bscContractsConfig: IContract[] = [
  {
    name: 'BONDINGFACTORYPROXY',
    address: process.env.BSC_BONDING_FACTORY_PROXY, //Bonding Factory Proxy
    events: ['BondingTokenCreated'],
    startBlock: Number(process.env.BSC_START_BLOCK) || 0,
    abi: BondingFactoryAbi,
  },
  // Add other BSC contracts here as needed
];

// Function to get contracts by chain
export const getContractsByChain = (chain: string): IContract[] => {
  switch (chain) {
    case 'Base':
      return baseContractsConfig;
    case 'BSC':
      return bscContractsConfig;
    default:
      throw new Error(`Unsupported chain: ${chain}`);
  }
};
