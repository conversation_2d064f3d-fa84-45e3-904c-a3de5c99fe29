[{"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "investor", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "_userID", "type": "string"}, {"indexed": false, "internalType": "string", "name": "_dividendID", "type": "string"}, {"indexed": false, "internalType": "address", "name": "token", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "netAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "adminFeeAmount", "type": "uint256"}], "name": "DividendDistributed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "fromAddress", "type": "address"}, {"indexed": false, "internalType": "address", "name": "to<PERSON><PERSON><PERSON>", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "orderID", "type": "string"}, {"indexed": false, "internalType": "address", "name": "token", "type": "address"}], "name": "ForceTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "fundFactory", "type": "address"}], "name": "FundFactoryUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "userAddress", "type": "address"}, {"indexed": false, "internalType": "address", "name": "token", "type": "address"}, {"indexed": false, "internalType": "string", "name": "actionID", "type": "string"}], "name": "IdentityUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint8", "name": "version", "type": "uint8"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "masterFactory", "type": "address"}], "name": "MasterFactoryUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "assetAddress", "type": "address"}, {"indexed": false, "internalType": "address", "name": "fundAddress", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "latestNAV", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "actionID", "type": "string"}], "name": "NAVUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "string", "name": "orderID", "type": "string"}, {"indexed": false, "internalType": "address", "name": "userAddress", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "orderValue", "type": "uint256"}], "name": "OrderCancelled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "_asset", "type": "address"}, {"indexed": false, "internalType": "address", "name": "_investor", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amountValue", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "tokens", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "orderID", "type": "string"}, {"indexed": false, "internalType": "string", "name": "coin", "type": "string"}], "name": "OrderCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "string", "name": "orderID", "type": "string"}, {"indexed": false, "internalType": "address", "name": "_Issuer", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "refundedValue", "type": "uint256"}], "name": "OrderRejected", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "string", "name": "orderID", "type": "string"}, {"indexed": false, "internalType": "address", "name": "_Issuer", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amountValue", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "tokens", "type": "uint256"}], "name": "OrderSettled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "token", "type": "address"}, {"indexed": false, "internalType": "address", "name": "userAddress", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "tokensBurned", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "principalAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "profitAmount", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "stableCoin", "type": "string"}, {"indexed": false, "internalType": "uint256", "name": "amountRedeemed", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "taxCollected", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "orderID", "type": "string"}], "name": "RedemptionAndBurn", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "string", "name": "coin", "type": "string"}, {"indexed": false, "internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}], "name": "StableCoinUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "fromAddress", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "orderID", "type": "string"}, {"indexed": false, "internalType": "address", "name": "token", "type": "address"}], "name": "TokensBurned", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "to<PERSON><PERSON><PERSON>", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "orderID", "type": "string"}, {"indexed": false, "internalType": "address", "name": "token", "type": "address"}], "name": "TokensMinted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "userAddress", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "isFrozen", "type": "bool"}, {"indexed": false, "internalType": "string", "name": "actionID", "type": "string"}, {"indexed": false, "internalType": "address", "name": "token", "type": "address"}], "name": "UserAddressFrozen", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "userAddress", "type": "address"}, {"indexed": false, "internalType": "address", "name": "token", "type": "address"}, {"indexed": false, "internalType": "uint16", "name": "country", "type": "uint16"}, {"indexed": false, "internalType": "string", "name": "actionID", "type": "string"}], "name": "UserCountryUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "userAddress", "type": "address"}, {"indexed": false, "internalType": "address", "name": "token", "type": "address"}, {"indexed": false, "internalType": "string", "name": "actionID", "type": "string"}], "name": "UserIdentityDeleted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "userAddress", "type": "address"}, {"indexed": false, "internalType": "address", "name": "onchainID", "type": "address"}, {"indexed": false, "internalType": "string", "name": "userID", "type": "string"}], "name": "UserIdentityRegistered", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "fromAddress", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "orderID", "type": "string"}, {"indexed": false, "internalType": "address", "name": "token", "type": "address"}], "name": "UserTokensFrozen", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "fromAddress", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "orderID", "type": "string"}, {"indexed": false, "internalType": "address", "name": "token", "type": "address"}], "name": "UserTokensUnFrozen", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "assetAddress", "type": "address"}, {"indexed": false, "internalType": "address", "name": "equityAddress", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "latestValuation", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "actionID", "type": "string"}], "name": "ValuationUpdated", "type": "event"}, {"inputs": [], "name": "FEE_DENOMINATOR", "outputs": [{"internalType": "uint16", "name": "", "type": "uint16"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_token", "type": "address"}, {"internalType": "address[]", "name": "_fromList", "type": "address[]"}, {"internalType": "uint256[]", "name": "_amounts", "type": "uint256[]"}, {"internalType": "string[]", "name": "orderIDs", "type": "string[]"}], "name": "batchBurnTokens", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_token", "type": "address"}, {"internalType": "address[]", "name": "_userAddresses", "type": "address[]"}, {"internalType": "address[]", "name": "_toAddresses", "type": "address[]"}, {"internalType": "uint256[]", "name": "_amounts", "type": "uint256[]"}, {"internalType": "string[]", "name": "orderIDs", "type": "string[]"}], "name": "batchForceTransferTokens", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_token", "type": "address"}, {"internalType": "address[]", "name": "_userAddresses", "type": "address[]"}, {"internalType": "uint256[]", "name": "_amounts", "type": "uint256[]"}, {"internalType": "string[]", "name": "orderIDs", "type": "string[]"}], "name": "batchFreezePartialTokens", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_token", "type": "address"}, {"internalType": "address[]", "name": "_toList", "type": "address[]"}, {"internalType": "uint256[]", "name": "_amounts", "type": "uint256[]"}, {"internalType": "string[]", "name": "orderIDs", "type": "string[]"}], "name": "batchMintTokens", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_token", "type": "address"}, {"internalType": "address[]", "name": "_userAddress", "type": "address[]"}, {"internalType": "uint256[]", "name": "_burnAmount", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "_principalAmount", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "_profitAmount", "type": "uint256[]"}, {"internalType": "string", "name": "coin", "type": "string"}, {"internalType": "string[]", "name": "orderID", "type": "string[]"}], "name": "batchRedemptionAndBurn", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "_userAddress", "type": "address[]"}, {"internalType": "contract IIdentity[]", "name": "_onchainID", "type": "address[]"}, {"internalType": "uint16[]", "name": "_country", "type": "uint16[]"}, {"internalType": "string[]", "name": "_userIDs", "type": "string[]"}, {"internalType": "address", "name": "_token", "type": "address"}], "name": "batchRegisterIdentity", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_token", "type": "address"}, {"internalType": "address[]", "name": "_userAddresses", "type": "address[]"}, {"internalType": "bool[]", "name": "_freeze", "type": "bool[]"}, {"internalType": "string[]", "name": "actionIDs", "type": "string[]"}], "name": "batchSetAddressFrozen", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string[]", "name": "orderIDs", "type": "string[]"}], "name": "batchSettlement", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_fund", "type": "address"}, {"internalType": "address[]", "name": "_address", "type": "address[]"}, {"internalType": "uint256[]", "name": "_dividend", "type": "uint256[]"}, {"internalType": "string[]", "name": "_userIds", "type": "string[]"}, {"internalType": "string[]", "name": "_dividendIds", "type": "string[]"}, {"internalType": "address", "name": "stableCoin_", "type": "address"}], "name": "batchShareDividend", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_token", "type": "address"}, {"internalType": "address[]", "name": "_userAddresses", "type": "address[]"}, {"internalType": "uint256[]", "name": "_amounts", "type": "uint256[]"}, {"internalType": "string[]", "name": "orderIDs", "type": "string[]"}], "name": "batchUnFreezePartialTokens", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_userAddress", "type": "address"}, {"internalType": "address", "name": "_token", "type": "address"}, {"internalType": "string", "name": "actionID", "type": "string"}], "name": "callDeleteIdentity", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_userAddress", "type": "address"}, {"internalType": "uint16", "name": "_country", "type": "uint16"}, {"internalType": "address", "name": "_token", "type": "address"}, {"internalType": "string", "name": "actionID", "type": "string"}], "name": "callUpdateCountry", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_userAddress", "type": "address"}, {"internalType": "contract IIdentity", "name": "_identity", "type": "address"}, {"internalType": "address", "name": "_token", "type": "address"}, {"internalType": "string", "name": "actionID", "type": "string"}], "name": "callUpdateIdentity", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "orderID", "type": "string"}], "name": "cancelOrder", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_token", "type": "address"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}, {"internalType": "uint256", "name": "_tokens", "type": "uint256"}, {"internalType": "string", "name": "orderID", "type": "string"}, {"internalType": "string", "name": "coin", "type": "string"}], "name": "deposit", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "fundFactory", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "_stablecoin", "type": "string"}], "name": "getStableCoin", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "stableCoin", "type": "address"}], "name": "getStableCoinName", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "stableCoin_", "type": "address[]"}, {"internalType": "address", "name": "_masterFactory", "type": "address"}, {"internalType": "address", "name": "_fundFactory", "type": "address"}], "name": "init", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "", "type": "string"}], "name": "investorOrders", "outputs": [{"internalType": "address", "name": "investor", "type": "address"}, {"internalType": "address", "name": "asset", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "tokens", "type": "uint256"}, {"internalType": "string", "name": "coin", "type": "string"}, {"internalType": "bool", "name": "status", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "isStableCoin", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "masterFactory", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "", "type": "string"}], "name": "pendingOrderAmount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_token", "type": "address"}, {"internalType": "address", "name": "_userAddress", "type": "address"}, {"internalType": "uint256", "name": "_burnAmount", "type": "uint256"}, {"internalType": "uint256", "name": "_principalAmount", "type": "uint256"}, {"internalType": "uint256", "name": "_profitAmount", "type": "uint256"}, {"internalType": "string", "name": "coin", "type": "string"}, {"internalType": "string", "name": "orderID", "type": "string"}], "name": "redemptionAndBurn", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "", "type": "string"}], "name": "redemptionStatus", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "orderID", "type": "string"}], "name": "rejectOrder", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_tokenAddr", "type": "address"}, {"internalType": "address", "name": "_to", "type": "address"}, {"internalType": "uint128", "name": "_amount", "type": "uint128"}], "name": "rescueAnyERC20Tokens", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_fundFactory", "type": "address"}], "name": "setFundFactory", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_masterFactory", "type": "address"}], "name": "setMasterFactory", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_token", "type": "address"}, {"internalType": "uint256", "name": "_latestNAV", "type": "uint256"}, {"internalType": "string", "name": "actionID", "type": "string"}], "name": "setNAV", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "coin", "type": "string"}, {"internalType": "address", "name": "_stablecoin", "type": "address"}], "name": "setStableCoin", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_token", "type": "address"}, {"internalType": "uint256", "name": "_latestValuation", "type": "uint256"}, {"internalType": "string", "name": "actionID", "type": "string"}], "name": "setValuation", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "orderID", "type": "string"}], "name": "settlement", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "totalPendingOrderAmount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]