import eventBlocksModel from "../models/eventBlocks.model";

class BlocksRepo {
  /**
   * Get the last fetched block number for a given chain and address.
   */
  public async getLastFetchedBlock(
    chain: string,
    address: string
  ): Promise<number | null> {
    const data = await eventBlocksModel.findOne({ chain, address }).lean();

    return data?.blockNumber ?? null;
  }

  /**
   * Upsert the block number for a given chain and address.
   */
  public async upsertBlockNumber(params: {
    chain: string;
    address: string;
    contractName: string;
    blockNumber: number;
  }): Promise<void> {
    const { chain, address, contractName, blockNumber } = params;

    await eventBlocksModel.findOneAndUpdate(
      { chain, address },
      {
        $set: {
          contractName,
          blockNumber,
        },
      },
      {
        new: true,
        upsert: true,
        setDefaultsOnInsert: true,
      }
    );
  }
}

export default new BlocksRepo();
