import * as dotenv from 'dotenv';
dotenv.config();

interface Kafka {
  clientId: string;
  brokers: [string];
  groupId: string;
}

interface Config {
  BASE_SEPOLIA_URL: string;
  BSC_RPC: string;
  MONGO_URI: string;
  KAFKA: Kafka;
  EVENT_BATCH_SIZE: number;
}

export const config: Config = {
  BASE_SEPOLIA_URL: process.env.BASE_SEPOLIA_URL,
  BSC_RPC: process.env.BSC_RPC,
  MONGO_URI: process.env.MONGO_URI,
  KAFKA: {
    clientId: process.env.KAFKA_CLIENT_ID,
    brokers: [process.env.KAFKA_BROKER],
    groupId: process.env.KAFKA_GROUP_ID,
  },
  EVENT_BATCH_SIZE: Number(process.env.EVENT_BATCH_SIZE) || 1000,
};
