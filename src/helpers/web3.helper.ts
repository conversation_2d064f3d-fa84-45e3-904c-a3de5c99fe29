import Web3, { Block, TransactionReceipt } from 'web3';
import { AbiItem } from 'web3-utils';
import { Contract } from 'web3';

export class Web3Helper {
  private web3: Web3;

  constructor(rpcUrl: string) {
    this.web3 = new Web3(rpcUrl);
  }

  public async getBlockNumber(): Promise<number> {
    try {
      const blockNumber = await this.web3.eth.getBlockNumber();
      console.log(`🔢 Current block number: ${blockNumber}`);
      return Number(blockNumber);
    } catch (error) {
      console.error('❌ Failed to fetch block number:', error);
      throw error;
    }
  }

  public async getBlock(blockNumber: number): Promise<any> {
    try {
      const block = await this.web3.eth.getBlock(blockNumber);
      console.log(`🔢 Block Info: ${block}`);
      return block;
    } catch (error) {
      console.error('❌ Failed to fetch block:', error);
      throw error;
    }
  }

  public isAddress(address: string): boolean {
    return this.web3.utils.isAddress(address);
  }

  public createContractInstance<T extends AbiItem[]>(abi: T, address: string): Contract<T> {
    if (!this.isAddress(address)) {
      throw new Error(`Invalid contract address: ${address}`);
    }

    const contract = new this.web3.eth.Contract(abi, address);
    console.log(`✅ Contract instance created at ${address}`);
    return contract;
  }

  public async getPastEvents<T extends AbiItem[]>(contract: Contract<T>, fromBlock: number, toBlock: number) {
    try {
      const events = await contract.getPastEvents('allEvents', {
        fromBlock,
        toBlock,
      });
      return events;
    } catch (error) {
      console.error('❌ Failed to fetch events:', error);
      throw error;
    }
  }
}
