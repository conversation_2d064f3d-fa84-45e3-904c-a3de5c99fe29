import cron from 'node-cron';
import { Web3Helper } from '../helpers/web3.helper';
import { EventService } from './event.service';
import { getContractsByChain } from '../configs/contract.config';

export class Scheduler {
  private eventService: EventService;
  private chain: string;
  private web3Helper: Web3Helper;

  constructor(chain: string, private rpcUrl: string) {
    this.chain = chain;
    this.web3Helper = new Web3Helper(this.rpcUrl);
    this.eventService = new EventService(this.web3Helper);
  }

  public start(): void {
    cron.schedule('*/10 * * * * *', async () => {
      // Get chain-specific contracts
      const chainContracts = getContractsByChain(this.chain);
      for (const contract of chainContracts) {
        try {
          const abi: any = contract.abi;
          const address = contract.address;

          // Skip if address is not defined for this chain
          if (!address) {
            console.log(`⚠️ Skipping ${contract.name} for ${this.chain} - address not configured`);
            continue;
          }

          const contractInstance = this.web3Helper.createContractInstance(abi, address);

          console.log(`🔄 Processing ${contract.name} on ${this.chain} chain`);
          await this.eventService.fetch(this.chain, contract, contractInstance);
        } catch (error) {
          console.error(`❌ Failed to fetch events for ${contract.name} on ${this.chain}:`, error);
        }
      }
    });
  }
}
