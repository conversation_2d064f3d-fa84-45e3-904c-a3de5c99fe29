# Admin Backend

## Description

# Requirements

Node.js Version: v20.6.0
Framework: Express
npm :10.2.3

## Table of Contents

1. [User](#user)
2. [Offerings](#offerings)
3. [Installation](#installation)
4. [Running the App](#running-the-app)
5. [Tree structure](#Tree structure)
6. [Support](#support)
7. [Stay in Touch](#stay-in-touch)
8. [License](#license)

## User

The admin service allows you to manage users, including:

- **Onboarding**: Facilitates the process of adding new users to the platform.
- **KYC Approval**: Enables reviewing and approving KYC (Know Your Customer) details of users to ensure compliance.

## Offerings

This service also provides the ability to:

- **Approve Issuers**: Review and approve issuer applications.
- **Issuer Management**: Manage issuer information and perform other administrative tasks related to onboarding issuers.

## Installation Process

1. Clone this repository to your local machine:

   ```
   git clone https://github.com/Libertum-Project/Backend-event-service.git
   ```

2. Install dependencies:

   ```
   npm install
   ```

3. Create a copy of the environment variables:

   ```
   cp .env.example .env
   ```

4. Update the .env file with your configuration values.

Now your environment is set up, and you can proceed with running the application as mentioned in the "Running the API" section. If you want to add some new variables, you also need to add them to interface and config object (Look `src/config/index.ts`)

## Running the API

### Development

To start the application in development mode, run:

```bash
npm install
```

Start the application in dev env:

```
npm run dev:start
```

Start the application in prod env:

```
npm run build
npm run prod
```

# tree structure

..
├── Dockerfile
├── env.example
├── logs
│ └── app.log
├── nodemon.json
├── package.json
├── package-lock.json
├── README.md
└── tsconfig.json

# Technologies Used

Node.js: Open-source JavaScript runtime environment.
Express: Web application framework for Node.js.
gRPC: High-performance RPC framework for microservices communication.
Redis: In-memory data structure store for caching and message brokering.
Kafka: Distributed event streaming platform for building real-time data pipelines.

## Support

This project is built using Node.js, which is an open-source, MIT-licensed framework. Node.js has a large, vibrant community that continually contributes to its growth. If you'd like to learn more or contribute, please visit the [Node.js GitHub repository](https://github.com/nodejs/node).

## Stay in touch

Website: [Your Website URL]
Email: [Your Support Email]
Twitter: [Your Twitter Handle]

## License

This project is [MIT licensed](LICENSE).
