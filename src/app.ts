import MongoService from './helpers/mongo.helper';
import KafkaService from './helpers/kafka.helper';
import { Scheduler } from './services/scheduler';
import { config } from './config';

class App {
  private schedulers: Array<{ start: () => void }>;
  constructor() {
    this.schedulers = [
      // new Scheduler('Base', config.BASE_SEPOLIA_URL),
      new Scheduler('BSC', config.BSC_RPC),
    ];
  }

  public async init(): Promise<void> {
    await MongoService.connect();
    await KafkaService.connect();
    this.schedulers.forEach((scheduler) => scheduler.start());

    console.log('🚀 Cron service initialized');
  }
}

export default new App();
