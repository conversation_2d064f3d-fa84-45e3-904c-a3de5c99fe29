[{"inputs": [{"internalType": "address", "name": "implementationAuthority_", "type": "address"}, {"internalType": "address", "name": "idFactory_", "type": "address"}, {"internalType": "address", "name": "wrapper_", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "_addr", "type": "address"}], "name": "Deployed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "_idFactory", "type": "address"}], "name": "IdFactorySet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "_implementationAuthority", "type": "address"}], "name": "ImplementationAuthoritySet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "_token", "type": "address"}, {"indexed": false, "internalType": "address", "name": "_ir", "type": "address"}, {"indexed": false, "internalType": "address", "name": "_irs", "type": "address"}, {"indexed": false, "internalType": "address", "name": "_tir", "type": "address"}, {"indexed": false, "internalType": "address", "name": "_ctr", "type": "address"}, {"indexed": false, "internalType": "address", "name": "_mc", "type": "address"}, {"indexed": false, "internalType": "string", "name": "_salt", "type": "string"}], "name": "TREXSuiteDeployed", "type": "event"}, {"inputs": [{"internalType": "string", "name": "_salt", "type": "string"}, {"components": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint8", "name": "decimals", "type": "uint8"}, {"internalType": "address", "name": "irs", "type": "address"}, {"internalType": "address", "name": "ONCHAINID", "type": "address"}, {"internalType": "bool", "name": "wrap", "type": "bool"}, {"internalType": "address[]", "name": "irAgents", "type": "address[]"}, {"internalType": "address[]", "name": "tokenAgents", "type": "address[]"}, {"internalType": "address[]", "name": "transferAgents", "type": "address[]"}, {"internalType": "address[]", "name": "complianceModules", "type": "address[]"}, {"internalType": "bytes[]", "name": "complianceSettings", "type": "bytes[]"}], "internalType": "struct ITREXFactory.TokenDetails", "name": "_tokenDetails", "type": "tuple"}, {"components": [{"internalType": "uint256[]", "name": "claimTopics", "type": "uint256[]"}, {"internalType": "address[]", "name": "issuers", "type": "address[]"}, {"internalType": "uint256[][]", "name": "issuerClaims", "type": "uint256[][]"}], "internalType": "struct ITREXFactory.ClaimDetails", "name": "_claimDetails", "type": "tuple"}], "name": "deployTREXSuite", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "deployedByMe", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getIdFactory", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getImplementationAuthority", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "_salt", "type": "string"}], "name": "getToken", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_contract", "type": "address"}, {"internalType": "address", "name": "_newOwner", "type": "address"}], "name": "recoverContractOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "idFactory_", "type": "address"}], "name": "setIdFactory", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "implementationAuthority_", "type": "address"}], "name": "setImplementationAuthority", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "wrapper_", "type": "address"}], "name": "setWrapper", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "", "type": "string"}], "name": "tokenDeployed", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_token", "type": "address"}], "name": "tokenDeployedByMe", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "wrapper", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}]