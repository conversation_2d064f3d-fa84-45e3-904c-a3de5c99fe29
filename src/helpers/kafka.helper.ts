import { Ka<PERSON>ka, Producer, Consumer } from 'kafkajs';
import { config } from '../config';

class KafkaService {
  private kafka: Kafka;
  public producer: Producer;
  public consumer: Consumer;

  constructor() {
    this.kafka = new Kafka({
      clientId: config.KAFKA.clientId,
      brokers: config.KAFKA.brokers,
    });

    this.producer = this.kafka.producer();
    this.consumer = this.kafka.consumer({ groupId: config.KAFKA.groupId });
  }

  public async connect(): Promise<void> {
    try {
      await this.producer.connect();
      await this.consumer.connect();
      console.log('✅ Kafka connected');
    } catch (error) {
      console.error('❌ Kafka connection error:', error);
      process.exit(1);
    }
  }

  public async emit(topic: string, message: any): Promise<void> {
    try {
      await this.producer.send({
        topic,
        messages: [
          {
            value: JSON.stringify(message),
          },
        ],
      });
      console.log(`📤 Message emitted to topic "${topic}"`);
    } catch (error) {
      console.error(`❌ Failed to emit message to topic "${topic}":`, error);
    }
  }
}

export default new KafkaService();
