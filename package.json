{"name": "event_s", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "npx tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@types/big.js": "^6.2.2", "@types/node": "^22.14.0", "@types/node-cron": "^3.0.11", "@types/web3": "^1.0.20", "typescript": "^5.8.3"}, "dependencies": {"big.js": "^6.2.2", "dotenv": "^16.4.7", "kafkajs": "^2.2.4", "mongoose": "^8.13.2", "node-cron": "^3.0.3", "web3": "^4.16.0"}}